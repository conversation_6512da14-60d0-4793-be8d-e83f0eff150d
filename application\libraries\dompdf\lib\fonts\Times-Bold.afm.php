<?php return array (
  'codeToName' => 
  array (
    32 => 'space',
    160 => 'space',
    33 => 'exclam',
    34 => 'quotedbl',
    35 => 'numbersign',
    36 => 'dollar',
    37 => 'percent',
    38 => 'ampersand',
    146 => 'quoteright',
    40 => 'parenleft',
    41 => 'parenright',
    42 => 'asterisk',
    43 => 'plus',
    44 => 'comma',
    45 => 'hyphen',
    173 => 'hyphen',
    46 => 'period',
    47 => 'slash',
    48 => 'zero',
    49 => 'one',
    50 => 'two',
    51 => 'three',
    52 => 'four',
    53 => 'five',
    54 => 'six',
    55 => 'seven',
    56 => 'eight',
    57 => 'nine',
    58 => 'colon',
    59 => 'semicolon',
    60 => 'less',
    61 => 'equal',
    62 => 'greater',
    63 => 'question',
    64 => 'at',
    65 => 'A',
    66 => 'B',
    67 => 'C',
    68 => 'D',
    69 => 'E',
    70 => 'F',
    71 => 'G',
    72 => 'H',
    73 => 'I',
    74 => 'J',
    75 => 'K',
    76 => 'L',
    77 => 'M',
    78 => 'N',
    79 => 'O',
    80 => 'P',
    81 => 'Q',
    82 => 'R',
    83 => 'S',
    84 => 'T',
    85 => 'U',
    86 => 'V',
    87 => 'W',
    88 => 'X',
    89 => 'Y',
    90 => 'Z',
    91 => 'bracketleft',
    92 => 'backslash',
    93 => 'bracketright',
    94 => 'asciicircum',
    95 => 'underscore',
    145 => 'quoteleft',
    97 => 'a',
    98 => 'b',
    99 => 'c',
    100 => 'd',
    101 => 'e',
    102 => 'f',
    103 => 'g',
    104 => 'h',
    105 => 'i',
    106 => 'j',
    107 => 'k',
    108 => 'l',
    109 => 'm',
    110 => 'n',
    111 => 'o',
    112 => 'p',
    113 => 'q',
    114 => 'r',
    115 => 's',
    116 => 't',
    117 => 'u',
    118 => 'v',
    119 => 'w',
    120 => 'x',
    121 => 'y',
    122 => 'z',
    123 => 'braceleft',
    124 => 'bar',
    125 => 'braceright',
    126 => 'asciitilde',
    161 => 'exclamdown',
    162 => 'cent',
    163 => 'sterling',
    165 => 'yen',
    131 => 'florin',
    167 => 'section',
    164 => 'currency',
    39 => 'quotesingle',
    147 => 'quotedblleft',
    171 => 'guillemotleft',
    139 => 'guilsinglleft',
    155 => 'guilsinglright',
    150 => 'endash',
    134 => 'dagger',
    135 => 'daggerdbl',
    183 => 'periodcentered',
    182 => 'paragraph',
    149 => 'bullet',
    130 => 'quotesinglbase',
    132 => 'quotedblbase',
    148 => 'quotedblright',
    187 => 'guillemotright',
    133 => 'ellipsis',
    137 => 'perthousand',
    191 => 'questiondown',
    96 => 'grave',
    180 => 'acute',
    136 => 'circumflex',
    152 => 'tilde',
    175 => 'macron',
    168 => 'dieresis',
    184 => 'cedilla',
    151 => 'emdash',
    198 => 'AE',
    170 => 'ordfeminine',
    216 => 'Oslash',
    140 => 'OE',
    186 => 'ordmasculine',
    230 => 'ae',
    248 => 'oslash',
    156 => 'oe',
    223 => 'germandbls',
    207 => 'Idieresis',
    233 => 'eacute',
    159 => 'Ydieresis',
    247 => 'divide',
    221 => 'Yacute',
    194 => 'Acircumflex',
    225 => 'aacute',
    219 => 'Ucircumflex',
    253 => 'yacute',
    234 => 'ecircumflex',
    220 => 'Udieresis',
    218 => 'Uacute',
    203 => 'Edieresis',
    169 => 'copyright',
    229 => 'aring',
    224 => 'agrave',
    227 => 'atilde',
    154 => 'scaron',
    237 => 'iacute',
    251 => 'ucircumflex',
    226 => 'acircumflex',
    231 => 'ccedilla',
    222 => 'Thorn',
    179 => 'threesuperior',
    210 => 'Ograve',
    192 => 'Agrave',
    215 => 'multiply',
    250 => 'uacute',
    255 => 'ydieresis',
    238 => 'icircumflex',
    202 => 'Ecircumflex',
    228 => 'adieresis',
    235 => 'edieresis',
    205 => 'Iacute',
    177 => 'plusminus',
    166 => 'brokenbar',
    174 => 'registered',
    200 => 'Egrave',
    142 => 'Zcaron',
    208 => 'Eth',
    199 => 'Ccedilla',
    193 => 'Aacute',
    196 => 'Adieresis',
    232 => 'egrave',
    211 => 'Oacute',
    243 => 'oacute',
    239 => 'idieresis',
    212 => 'Ocircumflex',
    217 => 'Ugrave',
    254 => 'thorn',
    178 => 'twosuperior',
    214 => 'Odieresis',
    181 => 'mu',
    236 => 'igrave',
    190 => 'threequarters',
    153 => 'trademark',
    204 => 'Igrave',
    189 => 'onehalf',
    244 => 'ocircumflex',
    241 => 'ntilde',
    201 => 'Eacute',
    188 => 'onequarter',
    138 => 'Scaron',
    176 => 'degree',
    242 => 'ograve',
    249 => 'ugrave',
    209 => 'Ntilde',
    245 => 'otilde',
    195 => 'Atilde',
    197 => 'Aring',
    213 => 'Otilde',
    206 => 'Icircumflex',
    172 => 'logicalnot',
    246 => 'odieresis',
    252 => 'udieresis',
    240 => 'eth',
    158 => 'zcaron',
    185 => 'onesuperior',
    128 => 'Euro',
  ),
  'isUnicode' => false,
  'FontName' => 'Times-Bold',
  'FullName' => 'Times Bold',
  'FamilyName' => 'Times',
  'Weight' => 'Bold',
  'ItalicAngle' => '0',
  'IsFixedPitch' => 'false',
  'CharacterSet' => 'ExtendedRoman',
  'FontBBox' => 
  array (
    0 => '-168',
    1 => '-218',
    2 => '1000',
    3 => '935',
  ),
  'UnderlinePosition' => '-100',
  'UnderlineThickness' => '50',
  'Version' => '002.000',
  'EncodingScheme' => 'WinAnsiEncoding',
  'CapHeight' => '676',
  'XHeight' => '461',
  'Ascender' => '683',
  'Descender' => '-217',
  'StdHW' => '44',
  'StdVW' => '139',
  'StartCharMetrics' => '317',
  'C' => 
  array (
    32 => 250.0,
    160 => 250.0,
    33 => 333.0,
    34 => 555.0,
    35 => 500.0,
    36 => 500.0,
    37 => 1000.0,
    38 => 833.0,
    146 => 333.0,
    40 => 333.0,
    41 => 333.0,
    42 => 500.0,
    43 => 570.0,
    44 => 250.0,
    45 => 333.0,
    173 => 333.0,
    46 => 250.0,
    47 => 278.0,
    48 => 500.0,
    49 => 500.0,
    50 => 500.0,
    51 => 500.0,
    52 => 500.0,
    53 => 500.0,
    54 => 500.0,
    55 => 500.0,
    56 => 500.0,
    57 => 500.0,
    58 => 333.0,
    59 => 333.0,
    60 => 570.0,
    61 => 570.0,
    62 => 570.0,
    63 => 500.0,
    64 => 930.0,
    65 => 722.0,
    66 => 667.0,
    67 => 722.0,
    68 => 722.0,
    69 => 667.0,
    70 => 611.0,
    71 => 778.0,
    72 => 778.0,
    73 => 389.0,
    74 => 500.0,
    75 => 778.0,
    76 => 667.0,
    77 => 944.0,
    78 => 722.0,
    79 => 778.0,
    80 => 611.0,
    81 => 778.0,
    82 => 722.0,
    83 => 556.0,
    84 => 667.0,
    85 => 722.0,
    86 => 722.0,
    87 => 1000.0,
    88 => 722.0,
    89 => 722.0,
    90 => 667.0,
    91 => 333.0,
    92 => 278.0,
    93 => 333.0,
    94 => 581.0,
    95 => 500.0,
    145 => 333.0,
    97 => 500.0,
    98 => 556.0,
    99 => 444.0,
    100 => 556.0,
    101 => 444.0,
    102 => 333.0,
    103 => 500.0,
    104 => 556.0,
    105 => 278.0,
    106 => 333.0,
    107 => 556.0,
    108 => 278.0,
    109 => 833.0,
    110 => 556.0,
    111 => 500.0,
    112 => 556.0,
    113 => 556.0,
    114 => 444.0,
    115 => 389.0,
    116 => 333.0,
    117 => 556.0,
    118 => 500.0,
    119 => 722.0,
    120 => 500.0,
    121 => 500.0,
    122 => 444.0,
    123 => 394.0,
    124 => 220.0,
    125 => 394.0,
    126 => 520.0,
    161 => 333.0,
    162 => 500.0,
    163 => 500.0,
    'fraction' => 167.0,
    165 => 500.0,
    131 => 500.0,
    167 => 500.0,
    164 => 500.0,
    39 => 278.0,
    147 => 500.0,
    171 => 500.0,
    139 => 333.0,
    155 => 333.0,
    'fi' => 556.0,
    'fl' => 556.0,
    150 => 500.0,
    134 => 500.0,
    135 => 500.0,
    183 => 250.0,
    182 => 540.0,
    149 => 350.0,
    130 => 333.0,
    132 => 500.0,
    148 => 500.0,
    187 => 500.0,
    133 => 1000.0,
    137 => 1000.0,
    191 => 500.0,
    96 => 333.0,
    180 => 333.0,
    136 => 333.0,
    152 => 333.0,
    175 => 333.0,
    'breve' => 333.0,
    'dotaccent' => 333.0,
    168 => 333.0,
    'ring' => 333.0,
    184 => 333.0,
    'hungarumlaut' => 333.0,
    'ogonek' => 333.0,
    'caron' => 333.0,
    151 => 1000.0,
    198 => 1000.0,
    170 => 300.0,
    'Lslash' => 667.0,
    216 => 778.0,
    140 => 1000.0,
    186 => 330.0,
    230 => 722.0,
    'dotlessi' => 278.0,
    'lslash' => 278.0,
    248 => 500.0,
    156 => 722.0,
    223 => 556.0,
    207 => 389.0,
    233 => 444.0,
    'abreve' => 500.0,
    'uhungarumlaut' => 556.0,
    'ecaron' => 444.0,
    159 => 722.0,
    247 => 570.0,
    221 => 722.0,
    194 => 722.0,
    225 => 500.0,
    219 => 722.0,
    253 => 500.0,
    'scommaaccent' => 389.0,
    234 => 444.0,
    'Uring' => 722.0,
    220 => 722.0,
    'aogonek' => 500.0,
    218 => 722.0,
    'uogonek' => 556.0,
    203 => 667.0,
    'Dcroat' => 722.0,
    'commaaccent' => 250.0,
    169 => 747.0,
    'Emacron' => 667.0,
    'ccaron' => 444.0,
    229 => 500.0,
    'Ncommaaccent' => 722.0,
    'lacute' => 278.0,
    224 => 500.0,
    'Tcommaaccent' => 667.0,
    'Cacute' => 722.0,
    227 => 500.0,
    'Edotaccent' => 667.0,
    154 => 389.0,
    'scedilla' => 389.0,
    237 => 278.0,
    'lozenge' => 494.0,
    'Rcaron' => 722.0,
    'Gcommaaccent' => 778.0,
    251 => 556.0,
    226 => 500.0,
    'Amacron' => 722.0,
    'rcaron' => 444.0,
    231 => 444.0,
    'Zdotaccent' => 667.0,
    222 => 611.0,
    'Omacron' => 778.0,
    'Racute' => 722.0,
    'Sacute' => 556.0,
    'dcaron' => 672.0,
    'Umacron' => 722.0,
    'uring' => 556.0,
    179 => 300.0,
    210 => 778.0,
    192 => 722.0,
    'Abreve' => 722.0,
    215 => 570.0,
    250 => 556.0,
    'Tcaron' => 667.0,
    'partialdiff' => 494.0,
    255 => 500.0,
    'Nacute' => 722.0,
    238 => 278.0,
    202 => 667.0,
    228 => 500.0,
    235 => 444.0,
    'cacute' => 444.0,
    'nacute' => 556.0,
    'umacron' => 556.0,
    'Ncaron' => 722.0,
    205 => 389.0,
    177 => 570.0,
    166 => 220.0,
    174 => 747.0,
    'Gbreve' => 778.0,
    'Idotaccent' => 389.0,
    'summation' => 600.0,
    200 => 667.0,
    'racute' => 444.0,
    'omacron' => 500.0,
    'Zacute' => 667.0,
    142 => 667.0,
    'greaterequal' => 549.0,
    208 => 722.0,
    199 => 722.0,
    'lcommaaccent' => 278.0,
    'tcaron' => 416.0,
    'eogonek' => 444.0,
    'Uogonek' => 722.0,
    193 => 722.0,
    196 => 722.0,
    232 => 444.0,
    'zacute' => 444.0,
    'iogonek' => 278.0,
    211 => 778.0,
    243 => 500.0,
    'amacron' => 500.0,
    'sacute' => 389.0,
    239 => 278.0,
    212 => 778.0,
    217 => 722.0,
    'Delta' => 612.0,
    254 => 556.0,
    178 => 300.0,
    214 => 778.0,
    181 => 556.0,
    236 => 278.0,
    'ohungarumlaut' => 500.0,
    'Eogonek' => 667.0,
    'dcroat' => 556.0,
    190 => 750.0,
    'Scedilla' => 556.0,
    'lcaron' => 394.0,
    'Kcommaaccent' => 778.0,
    'Lacute' => 667.0,
    153 => 1000.0,
    'edotaccent' => 444.0,
    204 => 389.0,
    'Imacron' => 389.0,
    'Lcaron' => 667.0,
    189 => 750.0,
    'lessequal' => 549.0,
    244 => 500.0,
    241 => 556.0,
    'Uhungarumlaut' => 722.0,
    201 => 667.0,
    'emacron' => 444.0,
    'gbreve' => 500.0,
    188 => 750.0,
    138 => 556.0,
    'Scommaaccent' => 556.0,
    'Ohungarumlaut' => 778.0,
    176 => 400.0,
    242 => 500.0,
    'Ccaron' => 722.0,
    249 => 556.0,
    'radical' => 549.0,
    'Dcaron' => 722.0,
    'rcommaaccent' => 444.0,
    209 => 722.0,
    245 => 500.0,
    'Rcommaaccent' => 722.0,
    'Lcommaaccent' => 667.0,
    195 => 722.0,
    'Aogonek' => 722.0,
    197 => 722.0,
    213 => 778.0,
    'zdotaccent' => 444.0,
    'Ecaron' => 667.0,
    'Iogonek' => 389.0,
    'kcommaaccent' => 556.0,
    'minus' => 570.0,
    206 => 389.0,
    'ncaron' => 556.0,
    'tcommaaccent' => 333.0,
    172 => 570.0,
    246 => 500.0,
    252 => 556.0,
    'notequal' => 549.0,
    'gcommaaccent' => 500.0,
    240 => 500.0,
    158 => 444.0,
    'ncommaaccent' => 556.0,
    185 => 300.0,
    'imacron' => 278.0,
    128 => 500.0,
  ),
  'CIDtoGID_Compressed' => true,
  'CIDtoGID' => 'eJwDAAAAAAE=',
  '_version_' => 6,
);