!function(r){var o={};function a(n){if(o[n])return o[n].exports;var e=o[n]={i:n,l:!1,exports:{}};return r[n].call(e.exports,e,e.exports,a),e.l=!0,e.exports}a.m=r,a.c=o,a.d=function(n,e,r){a.o(n,e)||Object.defineProperty(n,e,{enumerable:!0,get:r})},a.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})},a.t=function(e,n){if(1&n&&(e=a(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(a.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)a.d(r,o,function(n){return e[n]}.bind(null,o));return r},a.n=function(n){var e=n&&n.__esModule?function(){return n.default}:function(){return n};return a.d(e,"a",e),e},a.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},a.p="",a(a.s=14)}({"./app/assets/es6/pages/mail.js":function(module,exports){eval("class AppMail {\r\n\r\n    static init() {\r\n        $('#mail-list .mail-list .list-info').on('click', (e) => {\r\n            $('#mail-list').addClass('d-none')\r\n            $('#mail-content').removeClass('d-none')\r\n        })\r\n\r\n        $('#back').on('click', (e) => {\r\n            $('#mail-content').addClass('d-none')\r\n            $('#mail-list').removeClass('d-none')\r\n        })\r\n\r\n        $('.mail-open-nav').on('click', (e) => {\r\n            $('#mail-nav').addClass('nav-open')\r\n        })\r\n\r\n        $('.mail-close-nav').on('click', (e) => {\r\n            $('#mail-nav').removeClass('nav-open')\r\n        })\r\n\r\n        $('.mail-open-compose').on('click', (e) => {\r\n            $('#mail-compose').removeClass('d-none')\r\n            $('#mail-content').addClass('d-none')\r\n            $('#mail-list').addClass('d-none')\r\n            $('#mail-nav').removeClass('nav-open')\r\n        })\r\n\r\n        $('.mail-close-compose').on('click', (e) => {\r\n            $('#mail-compose').addClass('d-none')\r\n            $('#mail-content').addClass('d-none')\r\n            $('#mail-list').removeClass('d-none')\r\n        })\r\n\r\n        $(\"#checkAll\").on('change',function(){\r\n            $('#mail-list input[type=\"checkbox\"]').prop('checked',$(this).is(\":checked\"));\r\n        }); \r\n\r\n        new Quill('#mail-compose-editor', {\r\n            theme: 'snow'\r\n        });\r\n    }\r\n}\r\n\r\n$(() => { AppMail.init(); });\r\n\r\n\n\n//# sourceURL=webpack:///./app/assets/es6/pages/mail.js?")},14:function(module,exports,__webpack_require__){eval('module.exports = __webpack_require__(/*! C:\\Users\\<USER>\\Desktop\\themeforest selling\\Enlink-bootstrap\\v1.0.1\\demo\\app\\assets\\es6\\pages\\mail.js */"./app/assets/es6/pages/mail.js");\n\n\n//# sourceURL=webpack:///multi_./app/assets/es6/pages/mail.js?')}});