!function(r){var t={};function o(e){if(t[e])return t[e].exports;var n=t[e]={i:e,l:!1,exports:{}};return r[e].call(n.exports,n,n.exports,o),n.l=!0,n.exports}o.m=r,o.c=t,o.d=function(e,n,r){o.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(n,e){if(1&e&&(n=o(n)),8&e)return n;if(4&e&&"object"==typeof n&&n&&n.__esModule)return n;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:n}),2&e&&"string"!=typeof n)for(var t in n)o.d(r,t,function(e){return n[e]}.bind(null,t));return r},o.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(n,"a",n),n},o.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},o.p="",o(o.s=18)}({"./app/assets/es6/pages/slider.js":function(module,exports){eval("class ComponentSlider {\r\n\r\n    static init() {\r\n        const horizonPrimary = document.getElementById('horizon-primary');\r\n        const verticalPrimary = document.getElementById('vertical-default');\r\n        const rangeSlider = document.getElementById('range-slider');\r\n        const stepSlider = document.getElementById('step-slider');\r\n\r\n        noUiSlider.create(horizonPrimary, {\r\n            start: 60,\r\n            connect: \"lower\",\r\n            step: 1,\r\n            range: {\r\n                'min': 0,\r\n                'max': 100\r\n            }\r\n        });\r\n\r\n        noUiSlider.create(verticalPrimary, {\r\n            start: 60,\r\n            connect: \"lower\",\r\n            orientation: 'vertical',\r\n            step: 1,\r\n            range: {\r\n                'min': 0,\r\n                'max': 100\r\n            }\r\n        });\r\n\r\n        noUiSlider.create(stepSlider, {\r\n            start: 20,\r\n            connect: \"lower\",\r\n            range: {\r\n                min: 0,\r\n                max: 100\r\n            },\r\n            pips: {\r\n                mode: 'values',\r\n                values: [0,10,20,30,40,50,60,70,80,90,100],\r\n                density: 10\r\n            }\r\n        });\r\n\r\n        noUiSlider.create(rangeSlider, {\r\n            connect: true,\r\n            behaviour: 'tap',\r\n            start: [ 300, 800 ],\r\n            range: {\r\n                'min': [ 0 ],\r\n                'max': [ 1000 ]\r\n            }\r\n        });\r\n\r\n        const nodes = [\r\n            document.getElementById('range-min'), // 0\r\n            document.getElementById('range-max')  // 1\r\n        ];\r\n\r\n        // Display the slider value and how far the handle moved\r\n        // from the left edge of the slider.\r\n        rangeSlider.noUiSlider.on('update', function ( values, handle, unencoded, isTap, positions ) {\r\n            nodes[handle].innerHTML = '$' + values[handle] ;\r\n        });\r\n    }\r\n}\r\n\r\n$(() => { ComponentSlider.init(); });\r\n\r\n\n\n//# sourceURL=webpack:///./app/assets/es6/pages/slider.js?")},18:function(module,exports,__webpack_require__){eval('module.exports = __webpack_require__(/*! C:\\Users\\<USER>\\Desktop\\themeforest selling\\Enlink-bootstrap\\v1.0.1\\demo\\app\\assets\\es6\\pages\\slider.js */"./app/assets/es6/pages/slider.js");\n\n\n//# sourceURL=webpack:///multi_./app/assets/es6/pages/slider.js?')}});