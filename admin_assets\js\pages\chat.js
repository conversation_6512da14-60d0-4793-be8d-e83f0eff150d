!function(n){var r={};function o(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}o.m=n,o.c=r,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)o.d(n,r,function(e){return t[e]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=2)}({"./app/assets/es6/pages/chat.js":function(module,exports){eval("class Chat {\r\n\r\n    static init() {\r\n\r\n        const chartContent = '.chat-content'\r\n\r\n        $('.chat-user-list .chat-list-item').on('click', () => {\r\n            $(chartContent).addClass('open')\r\n        })\r\n\r\n        $('.chat-close').on('click', () => {\r\n            $(chartContent).removeClass('open')\r\n        })\r\n    }\r\n}\r\n\r\n$(() => { Chat.init(); });\r\n\r\n\n\n//# sourceURL=webpack:///./app/assets/es6/pages/chat.js?")},2:function(module,exports,__webpack_require__){eval('module.exports = __webpack_require__(/*! C:\\Users\\<USER>\\Desktop\\themeforest selling\\Enlink-bootstrap\\v1.0.1\\demo\\app\\assets\\es6\\pages\\chat.js */"./app/assets/es6/pages/chat.js");\n\n\n//# sourceURL=webpack:///multi_./app/assets/es6/pages/chat.js?')}});