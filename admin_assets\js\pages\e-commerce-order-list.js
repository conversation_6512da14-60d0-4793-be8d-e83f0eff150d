!function(t){var n={};function o(e){if(n[e])return n[e].exports;var r=n[e]={i:e,l:!1,exports:{}};return t[e].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.m=t,o.c=n,o.d=function(e,r,t){o.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:t})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(r,e){if(1&e&&(r=o(r)),8&e)return r;if(4&e&&"object"==typeof r&&r&&r.__esModule)return r;var t=Object.create(null);if(o.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:r}),2&e&&"string"!=typeof r)for(var n in r)o.d(t,n,function(e){return r[e]}.bind(null,n));return t},o.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(r,"a",r),r},o.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},o.p="",o(o.s=8)}({"./app/assets/es6/pages/e-commerce-order-list.js":function(module,exports){eval("class OrderList {\r\n\r\n    static init() {\r\n        $('.e-commerce-table').DataTable();\r\n\r\n        $(\"#checkAll\").on('change',function(){\r\n            $('.e-commerce-table input[type=\"checkbox\"]').prop('checked',$(this).is(\":checked\"));\r\n        }); \r\n    }\r\n}\r\n\r\n$(() => { OrderList.init(); });\r\n\r\n\n\n//# sourceURL=webpack:///./app/assets/es6/pages/e-commerce-order-list.js?")},8:function(module,exports,__webpack_require__){eval('module.exports = __webpack_require__(/*! C:\\Users\\<USER>\\Desktop\\themeforest selling\\Enlink-bootstrap\\v1.0.1\\demo\\app\\assets\\es6\\pages\\e-commerce-order-list.js */"./app/assets/es6/pages/e-commerce-order-list.js");\n\n\n//# sourceURL=webpack:///multi_./app/assets/es6/pages/e-commerce-order-list.js?')}});