<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Working_days extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Working_days_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'working_days/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'working_days/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'working_days/index.html';
            $config['first_url'] = base_url() . 'working_days/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Working_days_model->total_rows($q);
        $working_days = $this->Working_days_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'working_days_data' => $working_days,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('working_days/working_days_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Working_days_model->get_by_id($id);
        if ($row) {
            $data = array(
		'id' => $row->id,
		'day' => $row->day,
		'value' => $row->value,
	    );
            $this->load->view('working_days/working_days_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('working_days'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('working_days/create_action'),
	    'id' => set_value('id'),
	    'day' => set_value('day'),
	    'value' => set_value('value'),
	);
        $this->load->view('working_days/working_days_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'day' => $this->input->post('day',TRUE),
		'value' => $this->input->post('value',TRUE),
	    );

            $this->Working_days_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('working_days'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Working_days_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('working_days/update_action'),
		'id' => set_value('id', $row->id),
		'day' => set_value('day', $row->day),
		'value' => set_value('value', $row->value),
	    );
            $this->load->view('working_days/working_days_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('working_days'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('id', TRUE));
        } else {
            $data = array(
		'day' => $this->input->post('day',TRUE),
		'value' => $this->input->post('value',TRUE),
	    );

            $this->Working_days_model->update($this->input->post('id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('working_days'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Working_days_model->get_by_id($id);

        if ($row) {
            $this->Working_days_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('working_days'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('working_days'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('day', 'day', 'trim|required');
	$this->form_validation->set_rules('value', 'value', 'trim|required');

	$this->form_validation->set_rules('id', 'id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Working_days.php */
/* Location: ./application/controllers/Working_days.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-10-14 06:27:32 */
/* http://harviacode.com */