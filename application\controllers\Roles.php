<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Roles extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Roles_model');
        $this->load->library('form_validation');
    }

    public function index()
    {


        $data = array(
            'roles_data' => $this->Roles_model->get_all(),

        );
        $this->load->view('admin/header');
        $this->load->view('roles/roles_list', $data);
        $this->load->view('admin/footer');
    }

    public function read($id) 
    {
        $row = $this->Roles_model->get_by_id($id);
        if ($row) {
            $data = array(
		'id' => $row->id,
		'RoleName' => $row->RoleName,
	    );
            $this->load->view('roles/roles_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('roles'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('roles/create_action'),
	    'id' => set_value('id'),
	    'RoleName' => set_value('RoleName'),
	);
        $this->load->view('admin/header');
        $this->load->view('roles/roles_form', $data);
        $this->load->view('admin/footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'RoleName' => $this->input->post('RoleName',TRUE),
	    );
			$logger = array(

				'user_id' => $this->session->userdata('user_id'),
				'activity' => 'Create role '.' '.$data['RoleName']

			);
			log_activity($logger);

            $this->Roles_model->insert($data);
			$this->toaster->success('Success, employee role was created');

			redirect(site_url('roles'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Roles_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('roles/update_action'),
		'id' => set_value('id', $row->id),
		'RoleName' => set_value('RoleName', $row->RoleName),
	    );
            $this->load->view('admin/header');
            $this->load->view('roles/roles_form', $data);
            $this->load->view('admin/footer');
        } else {
			$this->toaster->error('Record not found');
            redirect(site_url('roles'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('id', TRUE));
        } else {
            $data = array(
		'RoleName' => $this->input->post('RoleName',TRUE),
	    );
			$logger = array(

				'user_id' => $this->session->userdata('user_id'),
				'activity' => 'Update role name to:'.' '.$data['RoleName']

			);
			log_activity($logger);



			$this->Roles_model->update($this->input->post('id', TRUE), $data);
			$this->toaster->success('Success, employee role was updated');
            redirect(site_url('roles'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Roles_model->get_by_id($id);

        if ($row) {
			$logger = array(

				'user_id' => $this->session->userdata('user_id'),
				'activity' => 'Delete role name'

			);
			log_activity($logger);
            $this->Roles_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('roles'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('roles'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('RoleName', 'rolename', 'trim|required');

	$this->form_validation->set_rules('id', 'id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Roles.php */
/* Location: ./application/controllers/Roles.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-10-14 06:27:32 */
/* http://harviacode.com */
