<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Sytem_date extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Sytem_date_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'sytem_date/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'sytem_date/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'sytem_date/index.html';
            $config['first_url'] = base_url() . 'sytem_date/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Sytem_date_model->total_rows($q);
        $sytem_date = $this->Sytem_date_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'sytem_date_data' => $sytem_date,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('sytem_date/sytem_date_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Sytem_date_model->get_by_id($id);
        if ($row) {
            $data = array(
		'id' => $row->id,
		's_date' => $row->s_date,
		'is_active' => $row->is_active,
	    );
            $this->load->view('sytem_date/sytem_date_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('sytem_date'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('sytem_date/create_action'),
	    'id' => set_value('id'),
	    's_date' => set_value('s_date'),
	    'is_active' => set_value('is_active'),
	);
        $this->load->view('sytem_date/sytem_date_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		's_date' => $this->input->post('s_date',TRUE),
		'is_active' => $this->input->post('is_active',TRUE),
	    );

            $this->Sytem_date_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('sytem_date'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Sytem_date_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('sytem_date/update_action'),
		'id' => set_value('id', $row->id),
		's_date' => set_value('s_date', $row->s_date),
		'is_active' => set_value('is_active', $row->is_active),
	    );
            $this->load->view('sytem_date/sytem_date_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('sytem_date'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('id', TRUE));
        } else {
            $data = array(
		's_date' => $this->input->post('s_date',TRUE),
		'is_active' => $this->input->post('is_active',TRUE),
	    );

            $this->Sytem_date_model->update($this->input->post('id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('sytem_date'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Sytem_date_model->get_by_id($id);

        if ($row) {
            $this->Sytem_date_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('sytem_date'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('sytem_date'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('s_date', 's date', 'trim|required');
	$this->form_validation->set_rules('is_active', 'is active', 'trim|required');

	$this->form_validation->set_rules('id', 'id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Sytem_date.php */
/* Location: ./application/controllers/Sytem_date.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-10-14 06:27:32 */
/* http://harviacode.com */