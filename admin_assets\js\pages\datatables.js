!function(a){var r={};function n(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return a[e].call(t.exports,t,t.exports,n),t.l=!0,t.exports}n.m=a,n.c=r,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(a,r,function(e){return t[e]}.bind(null,r));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=7)}({"./app/assets/es6/pages/datatables.js":function(module,exports){eval("class TablesDataTable {\r\n\r\n    static init() {\r\n        $('#data-table').DataTable();\r\n    }\r\n}\r\n\r\n$(() => { TablesDataTable.init(); });\r\n\r\n\n\n//# sourceURL=webpack:///./app/assets/es6/pages/datatables.js?")},7:function(module,exports,__webpack_require__){eval('module.exports = __webpack_require__(/*! C:\\Users\\<USER>\\Desktop\\themeforest selling\\Enlink-bootstrap\\v1.0.1\\demo\\app\\assets\\es6\\pages\\datatables.js */"./app/assets/es6/pages/datatables.js");\n\n\n//# sourceURL=webpack:///multi_./app/assets/es6/pages/datatables.js?')}});